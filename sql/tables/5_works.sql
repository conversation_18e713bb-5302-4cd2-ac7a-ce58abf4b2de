-- auto-generated definition
create table works
(
    id               bigint generated by default as identity
        primary key,
    created_at       timestamp with time zone default now() not null,
    updated_at       timestamp with time zone default now() not null,
    uid              varchar,
    input_text       varchar,
    revised_text     varchar,
    output_url       varchar,
    is_public        boolean                  default false,
    status           integer,
    user_id          varchar,
    is_origin        boolean,
    origin_language  varchar,
    current_language varchar,
    is_delete        boolean                  default false
);

comment on table works is 'works';

comment on column works.id is 'sequence id';

comment on column works.created_at is 'create time';

comment on column works.updated_at is 'update time';

comment on column works.uid is 'uid';

comment on column works.input_text is 'input_text';

comment on column works.revised_text is 'revised_text';

comment on column works.output_url is 'output_url';

comment on column works.is_public is 'is_public';

comment on column works.status is 'status';

comment on column works.user_id is 'user_id';

comment on column works.is_origin is 'is_origin';

comment on column works.origin_language is 'origin_language';

comment on column works.current_language is 'current_language';
