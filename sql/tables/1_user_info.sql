-- auto-generated definition
create table user_info
(
    id            bigint generated by default as identity
        primary key,
    created_at    timestamp with time zone default now() not null,
    updated_at    timestamp with time zone default now() not null,
    user_id       varchar,
    name          varchar,
    email         varchar,
    image         varchar,
    last_login_ip varchar
);

comment on table user_info is 'user info table';

comment on column user_info.id is 'sequence id';

comment on column user_info.created_at is 'create time';

comment on column user_info.updated_at is 'update time';

comment on column user_info.user_id is 'user uuid';

comment on column user_info.name is 'user name';

comment on column user_info.email is 'user email';

comment on column user_info.image is 'user avatar path';

comment on column user_info.last_login_ip is 'user last login ip';
