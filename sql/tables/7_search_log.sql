-- auto-generated definition
create table search_log
(
    id           bigint generated by default as identity
        primary key,
    created_at   timestamp with time zone default now() not null,
    search_text  varchar,
    result_count varchar,
    user_agent   varchar,
    search_ip    varchar
);

comment on table search_log is 'search_log';

comment on column search_log.id is '自增id';

comment on column search_log.created_at is 'created_at';

comment on column search_log.search_text is 'search_text';

comment on column search_log.result_count is 'result_count';

comment on column search_log.user_agent is 'user_agent';

comment on column search_log.search_ip is 'search_ip';
