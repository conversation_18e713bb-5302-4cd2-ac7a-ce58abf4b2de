#--------------------------------------------------------------------------------------------------------
# website URL
#--------------------------------------------------------------------------------------------------------
NEXT_PUBLIC_SITE_URL=

#--------------------------------------------------------------------------------------------------------
# website name
#--------------------------------------------------------------------------------------------------------
NEXT_PUBLIC_WEBSITE_NAME=

#--------------------------------------------------------------------------------------------------------
# image alt text
#--------------------------------------------------------------------------------------------------------
NEXT_PUBLIC_IMAGE_ALT_ADDITION_TEXT=

#--------------------------------------------------------------------------------------------------------
# domain name
#--------------------------------------------------------------------------------------------------------
NEXT_PUBLIC_DOMAIN_NAME=

#--------------------------------------------------------------------------------------------------------
# postgres config
#--------------------------------------------------------------------------------------------------------
POSTGRES_URL=

#--------------------------------------------------------------------------------------------------------
# Google auth config
# 0 代表不检查登录，则登录相关的按钮也不展示出来，1代表要检查
#--------------------------------------------------------------------------------------------------------
NEXT_PUBLIC_CHECK_GOOGLE_LOGIN=0
NEXT_PUBLIC_GOOGLE_CLIENT_ID=
GOOGLE_SECRET_ID=

#--------------------------------------------------------------------------------------------------------
# NEXTAUTH config      create command: openssl rand -base64 32
#--------------------------------------------------------------------------------------------------------
NEXTAUTH_URL=
NEXTAUTH_SECRET=

#--------------------------------------------------------------------------------------------------------
# Google gtag id
#--------------------------------------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_TAG_ID=

#--------------------------------------------------------------------------------------------------------
# Update these with your Stripe credentials from https://dashboard.stripe.com/apikeys
# 0 代表不检查支付，则支付页面也不展示出来，1代表要检查
#--------------------------------------------------------------------------------------------------------
NEXT_PUBLIC_CHECK_AVAILABLE_TIME=0
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
# 免费生成次数
FREE_TIMES=2

#--------------------------------------------------------------------------------------------------------
# replicate config
#--------------------------------------------------------------------------------------------------------
# replicate 生成结束后的回调地址，本地时我才用的 ngrok，线上的话就是你的域名
REPLICATE_WEBHOOK=
# replicate 的 API token，需要去你的 replicate 账号里面复制 https://replicate.com/account/api-tokens
REPLICATE_API_TOKEN=
# 生成贴纸的API版本 https://replicate.com/fofr/sticker-maker/versions ,最新的那个版本只生成一张图片了，下方这个版本是还会一次生成两张图片的
REPLICATE_API_VERSION="6443cc831f51eb01333f50b757157411d7cadb6215144cc721e3688b70004ad0"

#--------------------------------------------------------------------------------------------------------
# cloudflare R2 config
#--------------------------------------------------------------------------------------------------------
NEXT_PUBLIC_STORAGE_URL=
STORAGE_DOMAIN=
R2_BUCKET=
R2_ACCOUNT_ID=
R2_ENDPOINT=
R2_TOKEN_VALUE=
R2_ACCESS_KEY_ID=
R2_SECRET_ACCESS_KEY=

#--------------------------------------------------------------------------------------------------------
# openai config
#--------------------------------------------------------------------------------------------------------
OPENAI_API_KEY=
OPENAI_API_BASE_URL=https://openrouter.ai/api
OPENAI_API_MODEL="openai/gpt-3.5-turbo"

#--------------------------------------------------------------------------------------------------------
# stickers 里面每页显示多少条数据
#--------------------------------------------------------------------------------------------------------
NEXT_PUBLIC_PAGES_SIZE=24
