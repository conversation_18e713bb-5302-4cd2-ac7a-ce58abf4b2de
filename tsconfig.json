{"compilerOptions": {"baseUrl": "src", "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "strict": false, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"~/*": ["./*"]}, "isolatedModules": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules/*"]}